import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';
import { IndustrialDeviceComponent } from './IndustrialDeviceComponent';
import { DeviceType, DeviceStatus, DeviceConfig, DataQuality } from '../types';
import { Debug } from '../../utils/Debug';

/**
 * 传感器类型枚举
 */
export enum SensorType {
  TEMPERATURE = 'temperature',
  PRESSURE = 'pressure',
  HUMIDITY = 'humidity',
  VIBRATION = 'vibration',
  PROXIMITY = 'proximity',
  PHOTOELECTRIC = 'photoelectric',
  ULTRASONIC = 'ultrasonic',
  FORCE = 'force',
  FLOW = 'flow',
  LEVEL = 'level',
  PH = 'ph',
  CONDUCTIVITY = 'conductivity',
  OXYGEN = 'oxygen',
  CO2 = 'co2',
  SMOKE = 'smoke',
  GAS = 'gas'
}

/**
 * 传感器测量范围接口
 */
interface MeasurementRange {
  min: number;
  max: number;
  unit: string;
  precision: number;
  accuracy: number; // 精度百分比
}

/**
 * 校准数据接口
 */
interface CalibrationData {
  lastCalibration: Date;
  nextCalibration: Date;
  calibrationInterval: number; // 校准间隔（天）
  calibrationPoints: CalibrationPoint[];
  driftRate: number; // 漂移率 (%/月)
}

/**
 * 校准点接口
 */
interface CalibrationPoint {
  reference: number;
  measured: number;
  error: number;
  timestamp: Date;
}

/**
 * 传感器组件
 * 实现各种工业传感器的数字孪生功能
 */
export class SensorComponent extends IndustrialDeviceComponent {
  public readonly deviceType = DeviceType.TEMPERATURE_SENSOR; // 默认类型，会根据配置更新
  
  // 传感器特有属性
  public sensorType: SensorType;
  public measurementRange: MeasurementRange;
  public currentValue: number = 0;
  public rawValue: number = 0;
  public filteredValue: number = 0;
  public previousValue: number = 0;
  
  // 测量特性
  public responseTime: number = 1000;     // 响应时间 (ms)
  public samplingRate: number = 10;       // 采样率 (Hz)
  public resolution: number = 0.1;        // 分辨率
  public hysteresis: number = 0.5;        // 迟滞
  
  // 校准和漂移
  public calibrationData: CalibrationData;
  public zeroOffset: number = 0;          // 零点偏移
  public spanError: number = 0;           // 量程误差 (%)
  public linearityError: number = 0;      // 线性度误差 (%)
  
  // 环境影响
  public temperatureCoefficient: number = 0.01; // 温度系数 (%/°C)
  public ambientTemperature: number = 25;       // 环境温度 (°C)
  public humidityEffect: number = 0;             // 湿度影响 (%)
  
  // 信号处理
  public filterType: 'none' | 'lowpass' | 'highpass' | 'bandpass' = 'lowpass';
  public filterCutoff: number = 1;        // 滤波截止频率 (Hz)
  public noiseLevel: number = 0.1;        // 噪声水平 (%)
  
  // 状态监控
  public signalQuality: number = 100;     // 信号质量 (%)
  public connectionStatus: boolean = true; // 连接状态
  public lastMeasurement: Date = new Date();
  public measurementCount: number = 0;
  
  // 报警设置
  public alarmLimits = {
    highHigh: { enabled: false, value: 0, hysteresis: 0 },
    high: { enabled: false, value: 0, hysteresis: 0 },
    low: { enabled: false, value: 0, hysteresis: 0 },
    lowLow: { enabled: false, value: 0, hysteresis: 0 },
    rateOfChange: { enabled: false, value: 0, timeWindow: 60 }
  };

  constructor(entity: Entity, config?: DeviceConfig) {
    super(entity, config);
    
    // 从配置中获取传感器类型
    this.sensorType = (config?.parameters?.sensorType as SensorType) || SensorType.TEMPERATURE;
    
    // 初始化测量范围
    this.initializeMeasurementRange();
    
    // 初始化校准数据
    this.initializeCalibrationData();
    
    // 初始化传感器特有的数据点
    this.initializeSensorDataPoints();
    
    // 启动测量循环
    this.startMeasurementLoop();
    
    Debug.log('Sensor', `传感器组件已创建: ${this.deviceName} (${this.sensorType})`);
  }

  /**
   * 初始化测量范围
   */
  private initializeMeasurementRange(): void {
    const ranges: Record<SensorType, MeasurementRange> = {
      [SensorType.TEMPERATURE]: { min: -50, max: 200, unit: '°C', precision: 0.1, accuracy: 0.5 },
      [SensorType.PRESSURE]: { min: 0, max: 1000, unit: 'kPa', precision: 0.1, accuracy: 0.25 },
      [SensorType.HUMIDITY]: { min: 0, max: 100, unit: '%RH', precision: 0.1, accuracy: 2.0 },
      [SensorType.VIBRATION]: { min: 0, max: 100, unit: 'mm/s', precision: 0.01, accuracy: 1.0 },
      [SensorType.PROXIMITY]: { min: 0, max: 100, unit: 'mm', precision: 0.1, accuracy: 0.1 },
      [SensorType.PHOTOELECTRIC]: { min: 0, max: 1, unit: '', precision: 1, accuracy: 0 },
      [SensorType.ULTRASONIC]: { min: 50, max: 5000, unit: 'mm', precision: 1, accuracy: 0.1 },
      [SensorType.FORCE]: { min: 0, max: 10000, unit: 'N', precision: 1, accuracy: 0.5 },
      [SensorType.FLOW]: { min: 0, max: 1000, unit: 'L/min', precision: 0.1, accuracy: 1.0 },
      [SensorType.LEVEL]: { min: 0, max: 10000, unit: 'mm', precision: 1, accuracy: 0.1 },
      [SensorType.PH]: { min: 0, max: 14, unit: 'pH', precision: 0.01, accuracy: 0.1 },
      [SensorType.CONDUCTIVITY]: { min: 0, max: 2000, unit: 'μS/cm', precision: 1, accuracy: 1.0 },
      [SensorType.OXYGEN]: { min: 0, max: 25, unit: '%', precision: 0.1, accuracy: 0.5 },
      [SensorType.CO2]: { min: 0, max: 5000, unit: 'ppm', precision: 1, accuracy: 2.0 },
      [SensorType.SMOKE]: { min: 0, max: 100, unit: '%', precision: 0.1, accuracy: 1.0 },
      [SensorType.GAS]: { min: 0, max: 1000, unit: 'ppm', precision: 1, accuracy: 2.0 }
    };

    this.measurementRange = ranges[this.sensorType];
  }

  /**
   * 初始化校准数据
   */
  private initializeCalibrationData(): void {
    this.calibrationData = {
      lastCalibration: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
      nextCalibration: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000), // 335天后
      calibrationInterval: 365, // 一年校准一次
      calibrationPoints: [],
      driftRate: 0.1 // 0.1%/月
    };
  }

  /**
   * 初始化传感器数据点
   */
  private initializeSensorDataPoints(): void {
    const dataPoints = [
      // 测量值
      { tagId: 'current_value', name: '当前值', unit: this.measurementRange.unit },
      { tagId: 'raw_value', name: '原始值', unit: this.measurementRange.unit },
      { tagId: 'filtered_value', name: '滤波值', unit: this.measurementRange.unit },
      
      // 状态信息
      { tagId: 'signal_quality', name: '信号质量', unit: '%' },
      { tagId: 'connection_status', name: '连接状态', unit: '' },
      { tagId: 'measurement_count', name: '测量次数', unit: '' },
      { tagId: 'response_time', name: '响应时间', unit: 'ms' },
      
      // 校准信息
      { tagId: 'zero_offset', name: '零点偏移', unit: this.measurementRange.unit },
      { tagId: 'span_error', name: '量程误差', unit: '%' },
      { tagId: 'linearity_error', name: '线性度误差', unit: '%' },
      { tagId: 'drift_rate', name: '漂移率', unit: '%/月' },
      
      // 环境影响
      { tagId: 'ambient_temperature', name: '环境温度', unit: '°C' },
      { tagId: 'humidity_effect', name: '湿度影响', unit: '%' },
      { tagId: 'noise_level', name: '噪声水平', unit: '%' },
      
      // 报警状态
      { tagId: 'alarm_high_high', name: '高高报警', unit: '' },
      { tagId: 'alarm_high', name: '高报警', unit: '' },
      { tagId: 'alarm_low', name: '低报警', unit: '' },
      { tagId: 'alarm_low_low', name: '低低报警', unit: '' }
    ];
    
    dataPoints.forEach(dp => {
      this.updateDataPoint({
        tagId: dp.tagId,
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: 0,
        quality: DataQuality.GOOD,
        metadata: { name: dp.name, unit: dp.unit }
      });
    });
  }

  /**
   * 启动测量循环
   */
  private startMeasurementLoop(): void {
    const interval = 1000 / this.samplingRate; // 转换为毫秒
    
    setInterval(() => {
      this.performMeasurement();
    }, interval);
  }

  /**
   * 执行测量
   */
  private performMeasurement(): void {
    if (!this.connectionStatus || this.status !== DeviceStatus.RUNNING) {
      return;
    }

    try {
      // 生成模拟测量值
      this.rawValue = this.generateMeasurementValue();
      
      // 应用校准修正
      const calibratedValue = this.applyCalibratedCorrection(this.rawValue);
      
      // 应用滤波
      this.filteredValue = this.applyFiltering(calibratedValue);
      
      // 更新当前值
      this.previousValue = this.currentValue;
      this.currentValue = this.filteredValue;
      
      // 更新测量统计
      this.measurementCount++;
      this.lastMeasurement = new Date();
      
      // 检查报警条件
      this.checkAlarmConditions();
      
      // 更新数据点
      this.updateMeasurementDataPoints();
      
      // 模拟信号质量变化
      this.updateSignalQuality();
      
    } catch (error) {
      Debug.error('Sensor', `测量失败: ${this.deviceName}`, error);
      this.signalQuality = 0;
      this.connectionStatus = false;
    }
  }

  /**
   * 生成模拟测量值
   */
  private generateMeasurementValue(): number {
    const { min, max } = this.measurementRange;
    
    // 基础值（根据传感器类型生成合理的值）
    let baseValue: number;
    
    switch (this.sensorType) {
      case SensorType.TEMPERATURE:
        baseValue = 25 + Math.sin(Date.now() / 60000) * 10 + Math.random() * 5;
        break;
      case SensorType.PRESSURE:
        baseValue = 100 + Math.sin(Date.now() / 30000) * 20 + Math.random() * 10;
        break;
      case SensorType.HUMIDITY:
        baseValue = 50 + Math.sin(Date.now() / 120000) * 20 + Math.random() * 5;
        break;
      case SensorType.VIBRATION:
        baseValue = 5 + Math.random() * 10;
        break;
      case SensorType.PROXIMITY:
        baseValue = Math.random() > 0.8 ? 10 : 50;
        break;
      case SensorType.PHOTOELECTRIC:
        baseValue = Math.random() > 0.7 ? 1 : 0;
        break;
      default:
        baseValue = min + Math.random() * (max - min) * 0.5;
    }
    
    // 添加噪声
    const noise = (Math.random() - 0.5) * 2 * this.noiseLevel / 100 * baseValue;
    
    // 添加环境影响
    const tempEffect = (this.ambientTemperature - 25) * this.temperatureCoefficient / 100 * baseValue;
    
    // 添加漂移
    const driftEffect = this.calculateDrift();
    
    const finalValue = baseValue + noise + tempEffect + driftEffect;
    
    // 限制在测量范围内
    return Math.max(min, Math.min(max, finalValue));
  }

  /**
   * 应用校准修正
   * @param rawValue 原始值
   */
  private applyCalibratedCorrection(rawValue: number): number {
    // 应用零点偏移
    let correctedValue = rawValue - this.zeroOffset;
    
    // 应用量程误差修正
    correctedValue = correctedValue * (1 - this.spanError / 100);
    
    // 应用线性度误差修正（简化）
    const linearityCorrection = correctedValue * this.linearityError / 100 * 
      Math.sin(correctedValue / this.measurementRange.max * Math.PI);
    correctedValue -= linearityCorrection;
    
    return correctedValue;
  }

  /**
   * 应用滤波
   * @param value 输入值
   */
  private applyFiltering(value: number): number {
    if (this.filterType === 'none') {
      return value;
    }
    
    // 简单的低通滤波器
    const alpha = this.filterCutoff / (this.filterCutoff + this.samplingRate);
    return this.filteredValue * (1 - alpha) + value * alpha;
  }

  /**
   * 计算漂移
   */
  private calculateDrift(): number {
    const daysSinceCalibration = (Date.now() - this.calibrationData.lastCalibration.getTime()) / (24 * 60 * 60 * 1000);
    const monthsSinceCalibration = daysSinceCalibration / 30;
    
    return this.currentValue * this.calibrationData.driftRate / 100 * monthsSinceCalibration;
  }

  /**
   * 检查报警条件
   */
  private checkAlarmConditions(): void {
    const value = this.currentValue;
    
    // 高高报警
    if (this.alarmLimits.highHigh.enabled) {
      if (value > this.alarmLimits.highHigh.value) {
        this.triggerAlarm('high_high_alarm');
      } else if (value < this.alarmLimits.highHigh.value - this.alarmLimits.highHigh.hysteresis) {
        this.clearAlarm('high_high_alarm');
      }
    }
    
    // 高报警
    if (this.alarmLimits.high.enabled) {
      if (value > this.alarmLimits.high.value) {
        this.triggerAlarm('high_alarm');
      } else if (value < this.alarmLimits.high.value - this.alarmLimits.high.hysteresis) {
        this.clearAlarm('high_alarm');
      }
    }
    
    // 低报警
    if (this.alarmLimits.low.enabled) {
      if (value < this.alarmLimits.low.value) {
        this.triggerAlarm('low_alarm');
      } else if (value > this.alarmLimits.low.value + this.alarmLimits.low.hysteresis) {
        this.clearAlarm('low_alarm');
      }
    }
    
    // 低低报警
    if (this.alarmLimits.lowLow.enabled) {
      if (value < this.alarmLimits.lowLow.value) {
        this.triggerAlarm('low_low_alarm');
      } else if (value > this.alarmLimits.lowLow.value + this.alarmLimits.lowLow.hysteresis) {
        this.clearAlarm('low_low_alarm');
      }
    }
    
    // 变化率报警
    if (this.alarmLimits.rateOfChange.enabled) {
      const rateOfChange = Math.abs(this.currentValue - this.previousValue);
      if (rateOfChange > this.alarmLimits.rateOfChange.value) {
        this.triggerAlarm('rate_of_change_alarm');
      }
    }
  }

  /**
   * 更新测量数据点
   */
  private updateMeasurementDataPoints(): void {
    const now = new Date();
    
    this.updateDataPoint({
      tagId: 'current_value',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.currentValue,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'raw_value',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.rawValue,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'filtered_value',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.filteredValue,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'measurement_count',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.measurementCount,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 更新信号质量
   */
  private updateSignalQuality(): void {
    // 基于连接状态、噪声水平等计算信号质量
    let quality = 100;
    
    if (!this.connectionStatus) {
      quality = 0;
    } else {
      quality -= this.noiseLevel * 2;
      quality -= Math.abs(this.calculateDrift()) * 10;
      
      // 检查是否需要校准
      const daysSinceCalibration = (Date.now() - this.calibrationData.lastCalibration.getTime()) / (24 * 60 * 60 * 1000);
      if (daysSinceCalibration > this.calibrationData.calibrationInterval) {
        quality -= 20;
      }
    }
    
    this.signalQuality = Math.max(0, Math.min(100, quality));
    
    this.updateDataPoint({
      tagId: 'signal_quality',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.signalQuality,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 执行校准
   * @param referenceValues 参考值数组
   */
  public performCalibration(referenceValues: number[]): boolean {
    try {
      const calibrationPoints: CalibrationPoint[] = [];
      
      for (const refValue of referenceValues) {
        // 模拟测量参考值
        const measuredValue = this.generateMeasurementValue();
        const error = measuredValue - refValue;
        
        calibrationPoints.push({
          reference: refValue,
          measured: measuredValue,
          error: error,
          timestamp: new Date()
        });
      }
      
      // 计算校准参数
      this.calculateCalibrationParameters(calibrationPoints);
      
      // 更新校准数据
      this.calibrationData.calibrationPoints = calibrationPoints;
      this.calibrationData.lastCalibration = new Date();
      this.calibrationData.nextCalibration = new Date(Date.now() + this.calibrationData.calibrationInterval * 24 * 60 * 60 * 1000);
      
      Debug.log('Sensor', `传感器校准完成: ${this.deviceName}`);
      return true;
      
    } catch (error) {
      Debug.error('Sensor', `传感器校准失败: ${this.deviceName}`, error);
      return false;
    }
  }

  /**
   * 计算校准参数
   * @param points 校准点
   */
  private calculateCalibrationParameters(points: CalibrationPoint[]): void {
    if (points.length === 0) return;
    
    // 计算零点偏移（第一个点的误差）
    this.zeroOffset = points[0].error;
    
    // 计算量程误差（简化为平均误差百分比）
    const avgError = points.reduce((sum, point) => sum + Math.abs(point.error), 0) / points.length;
    this.spanError = (avgError / this.measurementRange.max) * 100;
    
    // 计算线性度误差（简化）
    this.linearityError = Math.max(...points.map(p => Math.abs(p.error))) / this.measurementRange.max * 100;
  }

  /**
   * 子类更新方法
   */
  protected onUpdate(): void {
    // 更新环境温度（模拟）
    this.ambientTemperature = 25 + Math.sin(Date.now() / 300000) * 5 + Math.random() * 2;
    
    // 更新湿度影响（模拟）
    this.humidityEffect = Math.random() * 2;
    
    // 检查校准到期
    if (Date.now() > this.calibrationData.nextCalibration.getTime()) {
      this.triggerAlarm('calibration_due');
    }
    
    // 检查信号质量
    if (this.signalQuality < 50) {
      this.triggerAlarm('signal_quality_low');
    }
  }

  /**
   * 获取传感器特有信息
   * @returns 传感器信息
   */
  public getSensorInfo(): any {
    return {
      ...this.getDeviceInfo(),
      sensorType: this.sensorType,
      measurementRange: this.measurementRange,
      currentValue: this.currentValue,
      rawValue: this.rawValue,
      filteredValue: this.filteredValue,
      responseTime: this.responseTime,
      samplingRate: this.samplingRate,
      resolution: this.resolution,
      signalQuality: this.signalQuality,
      connectionStatus: this.connectionStatus,
      calibrationData: this.calibrationData,
      alarmLimits: this.alarmLimits,
      measurementCount: this.measurementCount,
      lastMeasurement: this.lastMeasurement
    };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): SensorComponent {
    return new SensorComponent(this.entity!);
  }
}
