import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';
import { Quaternion } from '../../math/Quaternion';
import { IndustrialDeviceComponent } from './IndustrialDeviceComponent';
import { DeviceType, DeviceStatus, DeviceConfig, DataQuality } from '../types';
import { Debug } from '../../utils/Debug';

/**
 * 关节数据接口
 */
interface JointData {
  id: number;
  name: string;
  angle: number;          // 当前角度 (度)
  velocity: number;       // 角速度 (度/秒)
  torque: number;         // 扭矩 (Nm)
  temperature: number;    // 温度 (°C)
  minAngle: number;       // 最小角度限制
  maxAngle: number;       // 最大角度限制
  maxVelocity: number;    // 最大角速度
  maxTorque: number;      // 最大扭矩
}

/**
 * 运动指令接口
 */
interface MotionCommand {
  type: 'joint' | 'cartesian' | 'linear' | 'circular';
  targetPosition?: Vector3;
  targetRotation?: Quaternion;
  jointAngles?: number[];
  speed: number;
  acceleration: number;
  blendRadius?: number;
}

/**
 * 机器人程序接口
 */
interface RobotProgram {
  id: string;
  name: string;
  commands: MotionCommand[];
  currentStep: number;
  isRunning: boolean;
  cycleTime: number;
}

/**
 * 工具数据接口
 */
interface ToolData {
  id: string;
  name: string;
  weight: number;        // 工具重量 (kg)
  centerOfGravity: Vector3; // 重心位置
  tcpOffset: Vector3;    // 工具中心点偏移
  tcpRotation: Quaternion; // 工具中心点旋转
}

/**
 * 机械臂组件
 * 实现工业机械臂的数字孪生功能
 */
export class RobotArmComponent extends IndustrialDeviceComponent {
  public readonly deviceType = DeviceType.ROBOT_ARM;
  
  // 机械臂特有属性
  public joints: JointData[] = [];
  public endEffectorPosition: Vector3 = new Vector3();
  public endEffectorRotation: Quaternion = new Quaternion();
  public currentTool: ToolData | null = null;
  public payload: number = 0;           // 当前负载 (kg)
  public maxPayload: number = 10;       // 最大负载 (kg)
  public reach: number = 1000;          // 工作半径 (mm)
  public repeatability: number = 0.1;   // 重复定位精度 (mm)
  
  // 运动状态
  public isMoving: boolean = false;
  public currentProgram: RobotProgram | null = null;
  public motionQueue: MotionCommand[] = [];
  public safetyZones: SafetyZone[] = [];
  
  // 性能数据
  public cycleCount: number = 0;
  public totalOperatingTime: number = 0;
  public averageCycleTime: number = 0;
  public collisionCount: number = 0;
  
  // 安全状态
  public emergencyStop: boolean = false;
  public safetyFenceStatus: boolean = true;
  public teachMode: boolean = false;
  public autoMode: boolean = true;

  constructor(entity: Entity, config?: DeviceConfig) {
    super(entity, config);
    
    // 初始化关节数据
    this.initializeJoints();
    
    // 初始化机械臂特有的数据点
    this.initializeRobotDataPoints();
    
    Debug.log('RobotArm', `机械臂组件已创建: ${this.deviceName}`);
  }

  /**
   * 初始化关节数据
   */
  private initializeJoints(): void {
    // 默认6轴机械臂配置
    const jointConfigs = [
      { name: 'Base', minAngle: -180, maxAngle: 180, maxVelocity: 120, maxTorque: 100 },
      { name: 'Shoulder', minAngle: -90, maxAngle: 90, maxVelocity: 120, maxTorque: 150 },
      { name: 'Elbow', minAngle: -170, maxAngle: 170, maxVelocity: 150, maxTorque: 100 },
      { name: 'Wrist1', minAngle: -180, maxAngle: 180, maxVelocity: 180, maxTorque: 50 },
      { name: 'Wrist2', minAngle: -120, maxAngle: 120, maxVelocity: 180, maxTorque: 50 },
      { name: 'Wrist3', minAngle: -360, maxAngle: 360, maxVelocity: 180, maxTorque: 50 }
    ];

    this.joints = jointConfigs.map((config, index) => ({
      id: index + 1,
      name: config.name,
      angle: 0,
      velocity: 0,
      torque: 0,
      temperature: 25,
      minAngle: config.minAngle,
      maxAngle: config.maxAngle,
      maxVelocity: config.maxVelocity,
      maxTorque: config.maxTorque
    }));
  }

  /**
   * 初始化机械臂数据点
   */
  private initializeRobotDataPoints(): void {
    const dataPoints = [
      // 关节数据
      ...this.joints.map(joint => [
        { tagId: `joint${joint.id}_angle`, name: `关节${joint.id}角度`, unit: '°' },
        { tagId: `joint${joint.id}_velocity`, name: `关节${joint.id}速度`, unit: '°/s' },
        { tagId: `joint${joint.id}_torque`, name: `关节${joint.id}扭矩`, unit: 'Nm' },
        { tagId: `joint${joint.id}_temperature`, name: `关节${joint.id}温度`, unit: '°C' }
      ]).flat(),
      
      // 末端执行器数据
      { tagId: 'tcp_position_x', name: 'TCP X坐标', unit: 'mm' },
      { tagId: 'tcp_position_y', name: 'TCP Y坐标', unit: 'mm' },
      { tagId: 'tcp_position_z', name: 'TCP Z坐标', unit: 'mm' },
      { tagId: 'tcp_rotation_rx', name: 'TCP RX旋转', unit: '°' },
      { tagId: 'tcp_rotation_ry', name: 'TCP RY旋转', unit: '°' },
      { tagId: 'tcp_rotation_rz', name: 'TCP RZ旋转', unit: '°' },
      
      // 状态数据
      { tagId: 'payload', name: '当前负载', unit: 'kg' },
      { tagId: 'cycle_count', name: '循环次数', unit: '' },
      { tagId: 'cycle_time', name: '循环时间', unit: 's' },
      { tagId: 'is_moving', name: '运动状态', unit: '' },
      { tagId: 'program_running', name: '程序运行', unit: '' },
      { tagId: 'emergency_stop', name: '急停状态', unit: '' }
    ];
    
    dataPoints.forEach(dp => {
      this.updateDataPoint({
        tagId: dp.tagId,
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: 0,
        quality: DataQuality.GOOD,
        metadata: { name: dp.name, unit: dp.unit }
      });
    });
  }

  /**
   * 移动到指定位置（笛卡尔坐标）
   * @param position 目标位置
   * @param rotation 目标旋转
   * @param speed 移动速度 (0-100%)
   */
  public async moveToPosition(position: Vector3, rotation: Quaternion, speed: number = 50): Promise<boolean> {
    if (this.emergencyStop || !this.autoMode) {
      Debug.warn('RobotArm', `机械臂无法移动: ${this.deviceName} - 急停或非自动模式`);
      return false;
    }

    try {
      // 检查位置是否在工作范围内
      if (!this.isPositionReachable(position)) {
        Debug.warn('RobotArm', `目标位置超出工作范围: ${this.deviceName}`);
        return false;
      }

      // 逆运动学求解
      const jointAngles = await this.solveInverseKinematics(position, rotation);
      if (!jointAngles) {
        Debug.warn('RobotArm', `逆运动学求解失败: ${this.deviceName}`);
        return false;
      }

      // 执行运动
      const motionCommand: MotionCommand = {
        type: 'cartesian',
        targetPosition: position,
        targetRotation: rotation,
        jointAngles,
        speed: speed / 100,
        acceleration: 0.5
      };

      return await this.executeMotion(motionCommand);

    } catch (error) {
      Debug.error('RobotArm', `移动到位置失败: ${this.deviceName}`, error);
      return false;
    }
  }

  /**
   * 移动关节到指定角度
   * @param jointAngles 关节角度数组
   * @param speed 移动速度 (0-100%)
   */
  public async moveJoints(jointAngles: number[], speed: number = 50): Promise<boolean> {
    if (this.emergencyStop || !this.autoMode) {
      Debug.warn('RobotArm', `机械臂无法移动: ${this.deviceName} - 急停或非自动模式`);
      return false;
    }

    try {
      // 检查关节角度限制
      if (!this.validateJointAngles(jointAngles)) {
        Debug.warn('RobotArm', `关节角度超出限制: ${this.deviceName}`);
        return false;
      }

      const motionCommand: MotionCommand = {
        type: 'joint',
        jointAngles,
        speed: speed / 100,
        acceleration: 0.5
      };

      return await this.executeMotion(motionCommand);

    } catch (error) {
      Debug.error('RobotArm', `关节移动失败: ${this.deviceName}`, error);
      return false;
    }
  }

  /**
   * 执行运动指令
   * @param command 运动指令
   */
  private async executeMotion(command: MotionCommand): Promise<boolean> {
    this.isMoving = true;
    this.updateDataPoint({
      tagId: 'is_moving',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: true,
      quality: DataQuality.GOOD
    });

    try {
      // 模拟运动过程
      const startTime = Date.now();
      const duration = this.calculateMotionDuration(command);

      // 插值运动
      await this.interpolateMotion(command, duration);

      // 更新位置和关节角度
      if (command.jointAngles) {
        this.updateJointAngles(command.jointAngles);
      }

      if (command.targetPosition && command.targetRotation) {
        this.endEffectorPosition.copy(command.targetPosition);
        this.endEffectorRotation.copy(command.targetRotation);
      }

      // 更新数据点
      this.updatePositionDataPoints();
      this.updateJointDataPoints();

      const actualDuration = (Date.now() - startTime) / 1000;
      Debug.log('RobotArm', `运动完成: ${this.deviceName} - 耗时 ${actualDuration.toFixed(2)}s`);

      return true;

    } catch (error) {
      Debug.error('RobotArm', `运动执行失败: ${this.deviceName}`, error);
      return false;
    } finally {
      this.isMoving = false;
      this.updateDataPoint({
        tagId: 'is_moving',
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: false,
        quality: DataQuality.GOOD
      });
    }
  }

  /**
   * 逆运动学求解
   * @param position 目标位置
   * @param rotation 目标旋转
   * @returns 关节角度数组
   */
  private async solveInverseKinematics(position: Vector3, rotation: Quaternion): Promise<number[] | null> {
    // 简化的逆运动学求解（实际应用中需要使用专业的IK算法）
    try {
      // 这里使用模拟的IK求解
      const jointAngles = this.joints.map((joint, index) => {
        // 基于目标位置计算关节角度（简化算法）
        const baseAngle = Math.atan2(position.y, position.x) * 180 / Math.PI;
        const reach = Math.sqrt(position.x * position.x + position.y * position.y + position.z * position.z);
        
        switch (index) {
          case 0: // Base joint
            return baseAngle;
          case 1: // Shoulder joint
            return Math.asin(position.z / reach) * 180 / Math.PI;
          case 2: // Elbow joint
            return -90 + Math.random() * 180;
          default: // Wrist joints
            return Math.random() * 60 - 30;
        }
      });

      // 验证解的有效性
      if (this.validateJointAngles(jointAngles)) {
        return jointAngles;
      }

      return null;
    } catch (error) {
      Debug.error('RobotArm', `IK求解失败: ${this.deviceName}`, error);
      return null;
    }
  }

  /**
   * 验证关节角度是否在限制范围内
   * @param jointAngles 关节角度数组
   */
  private validateJointAngles(jointAngles: number[]): boolean {
    if (jointAngles.length !== this.joints.length) {
      return false;
    }

    return jointAngles.every((angle, index) => {
      const joint = this.joints[index];
      return angle >= joint.minAngle && angle <= joint.maxAngle;
    });
  }

  /**
   * 检查位置是否可达
   * @param position 目标位置
   */
  private isPositionReachable(position: Vector3): boolean {
    const distance = position.length();
    return distance <= this.reach;
  }

  /**
   * 计算运动持续时间
   * @param command 运动指令
   */
  private calculateMotionDuration(command: MotionCommand): number {
    // 简化的时间计算
    const baseTime = 2.0; // 基础时间2秒
    const speedFactor = 1 / command.speed;
    return baseTime * speedFactor;
  }

  /**
   * 插值运动
   * @param command 运动指令
   * @param duration 持续时间
   */
  private async interpolateMotion(command: MotionCommand, duration: number): Promise<void> {
    const steps = Math.ceil(duration * 10); // 10Hz更新频率
    const stepTime = duration / steps;

    for (let i = 0; i <= steps; i++) {
      const t = i / steps;
      
      // 更新关节角度（线性插值）
      if (command.jointAngles) {
        const currentAngles = this.joints.map((joint, index) => {
          const targetAngle = command.jointAngles![index];
          return joint.angle + (targetAngle - joint.angle) * t;
        });
        
        this.updateJointAngles(currentAngles);
      }

      // 模拟运动延迟
      await new Promise(resolve => setTimeout(resolve, stepTime * 1000));
    }
  }

  /**
   * 更新关节角度
   * @param angles 新的关节角度
   */
  private updateJointAngles(angles: number[]): void {
    angles.forEach((angle, index) => {
      if (index < this.joints.length) {
        this.joints[index].angle = angle;
        
        // 模拟关节速度和扭矩
        this.joints[index].velocity = Math.random() * 50;
        this.joints[index].torque = Math.random() * this.joints[index].maxTorque * 0.5;
        this.joints[index].temperature = 25 + Math.random() * 15;
      }
    });
  }

  /**
   * 更新位置数据点
   */
  private updatePositionDataPoints(): void {
    const now = new Date();
    
    // 更新TCP位置
    this.updateDataPoint({
      tagId: 'tcp_position_x',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.endEffectorPosition.x,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tcp_position_y',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.endEffectorPosition.y,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tcp_position_z',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.endEffectorPosition.z,
      quality: DataQuality.GOOD
    });

    // 更新TCP旋转（转换为欧拉角）
    const euler = this.endEffectorRotation.toEuler();
    this.updateDataPoint({
      tagId: 'tcp_rotation_rx',
      deviceId: this.deviceId,
      timestamp: now,
      value: euler.x * 180 / Math.PI,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tcp_rotation_ry',
      deviceId: this.deviceId,
      timestamp: now,
      value: euler.y * 180 / Math.PI,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tcp_rotation_rz',
      deviceId: this.deviceId,
      timestamp: now,
      value: euler.z * 180 / Math.PI,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 更新关节数据点
   */
  private updateJointDataPoints(): void {
    const now = new Date();
    
    this.joints.forEach(joint => {
      this.updateDataPoint({
        tagId: `joint${joint.id}_angle`,
        deviceId: this.deviceId,
        timestamp: now,
        value: joint.angle,
        quality: DataQuality.GOOD
      });

      this.updateDataPoint({
        tagId: `joint${joint.id}_velocity`,
        deviceId: this.deviceId,
        timestamp: now,
        value: joint.velocity,
        quality: DataQuality.GOOD
      });

      this.updateDataPoint({
        tagId: `joint${joint.id}_torque`,
        deviceId: this.deviceId,
        timestamp: now,
        value: joint.torque,
        quality: DataQuality.GOOD
      });

      this.updateDataPoint({
        tagId: `joint${joint.id}_temperature`,
        deviceId: this.deviceId,
        timestamp: now,
        value: joint.temperature,
        quality: DataQuality.GOOD
      });
    });
  }

  /**
   * 设置急停状态
   * @param enabled 是否启用急停
   */
  public setEmergencyStop(enabled: boolean): void {
    this.emergencyStop = enabled;
    
    if (enabled) {
      this.isMoving = false;
      this.updateStatus(DeviceStatus.ERROR);
      Debug.warn('RobotArm', `机械臂急停: ${this.deviceName}`);
    } else {
      this.updateStatus(DeviceStatus.ONLINE);
      Debug.log('RobotArm', `机械臂急停解除: ${this.deviceName}`);
    }

    this.updateDataPoint({
      tagId: 'emergency_stop',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: enabled,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 子类更新方法
   */
  protected onUpdate(): void {
    // 更新负载数据
    this.updateDataPoint({
      tagId: 'payload',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.payload,
      quality: DataQuality.GOOD
    });

    // 更新循环计数
    this.updateDataPoint({
      tagId: 'cycle_count',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.cycleCount,
      quality: DataQuality.GOOD
    });

    // 检查关节温度报警
    this.joints.forEach(joint => {
      if (joint.temperature > 70) {
        this.triggerAlarm(`joint${joint.id}_overheat`);
      }
    });

    // 检查负载报警
    if (this.payload > this.maxPayload * 0.9) {
      this.triggerAlarm('payload_warning');
    }
  }

  /**
   * 获取机械臂特有信息
   * @returns 机械臂信息
   */
  public getRobotInfo(): any {
    return {
      ...this.getDeviceInfo(),
      joints: this.joints,
      endEffectorPosition: this.endEffectorPosition,
      endEffectorRotation: this.endEffectorRotation,
      currentTool: this.currentTool,
      payload: this.payload,
      maxPayload: this.maxPayload,
      reach: this.reach,
      repeatability: this.repeatability,
      isMoving: this.isMoving,
      cycleCount: this.cycleCount,
      emergencyStop: this.emergencyStop,
      autoMode: this.autoMode
    };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): RobotArmComponent {
    return new RobotArmComponent(this.entity!);
  }
}

/**
 * 安全区域接口
 */
interface SafetyZone {
  id: string;
  name: string;
  type: 'box' | 'sphere' | 'cylinder';
  position: Vector3;
  size: Vector3;
  enabled: boolean;
}
